"""
Test script to validate database connection and basic functionality
"""
import os
from langchain_community.utilities import SQLDatabase

# Database connection string
DB_CONN_STR = (
    r'DRIVER={ODBC Driver 17 for SQL Server};'
    r'SERVER=HAARLT0397\SQLEXPRESS;'
    r'DATABASE=Visa_Data_05262025;'
    r'Trusted_Connection=yes;'
)

def test_db_connection():
    """Test database connection"""
    try:
        print("Testing database connection...")
        db = SQLDatabase.from_uri(f"mssql+pyodbc:///?odbc_connect={DB_CONN_STR}")
        
        print(f"Database dialect: {db.dialect}")
        print(f"Available tables: {db.get_usable_table_names()}")
        
        # Test a simple query
        result = db.run("SELECT COUNT(*) as table_count FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'")
        print(f"Number of tables in database: {result}")
        
        print("✅ Database connection successful!")
        return True
        
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def test_api_key():
    """Test API key availability"""
    try:
        api_key = os.environ.get("GOOGLE_API_KEY")
        if api_key:
            print("✅ Google API key found in environment")
            return True
        else:
            print("❌ Google API key not found in environment")
            print("Please set GOOGLE_API_KEY environment variable or configure Streamlit secrets")
            return False
    except Exception as e:
        print(f"❌ Error checking API key: {e}")
        return False

if __name__ == "__main__":
    print("=== SQL Agent Connection Test ===\n")
    
    api_test = test_api_key()
    db_test = test_db_connection()
    
    print(f"\n=== Test Results ===")
    print(f"API Key: {'✅ PASS' if api_test else '❌ FAIL'}")
    print(f"Database: {'✅ PASS' if db_test else '❌ FAIL'}")
    
    if api_test and db_test:
        print("\n🎉 All tests passed! You can run the Streamlit application.")
    else:
        print("\n⚠️  Some tests failed. Please fix the issues before running the application.")
