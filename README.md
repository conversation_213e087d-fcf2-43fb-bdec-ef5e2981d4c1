# SQL Agent Application

This application provides two approaches for querying SQL databases using natural language:

1. **Chain Approach** - A structured workflow with human-in-the-loop approval
2. **Agent Approach** - An autonomous agent that can make multiple queries and self-correct

## Features

### Chain Approach (`streamlit_sql_agent.py`)
- Human-in-the-loop query approval
- Step-by-step execution with LangGraph
- Memory persistence across conversations
- Structured query generation with type safety

### Enhanced Agent (`enhanced_sql_agent.py`)
- **Dual Approach**: Choose between Chain (with approval) or Agent (autonomous)
- **SQL Toolkit**: Uses LangChain's SQLDatabaseToolkit for comprehensive database operations
- **Better UI**: Enhanced Streamlit interface with database schema exploration
- **Streaming Output**: Real-time display of agent reasoning and tool usage
- **Error Handling**: Improved error handling and user feedback

## Setup

1. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Configure API Key**:
   
   **Option A**: Environment Variable
   ```bash
   export GOOGLE_API_KEY="your_google_api_key_here"
   ```
   
   **Option B**: Streamlit Secrets
   - Edit `.streamlit/secrets.toml`
   - Add your Google API key:
     ```toml
     GOOGLE_API_KEY = "your_google_api_key_here"
     ```

3. **Database Configuration**:
   - Update the `DB_CONN_STR` in the Python files with your SQL Server connection details
   - Ensure you have the ODBC Driver 17 for SQL Server installed

## Usage

### Run the Original Application:
```bash
streamlit run streamlit_sql_agent.py
```

### Run the Enhanced Application:
```bash
streamlit run enhanced_sql_agent.py
```

## Key Improvements Made

### Security
- ✅ Removed hardcoded API keys
- ✅ Added support for environment variables and Streamlit secrets
- ✅ Secure credential management

### Code Quality
- ✅ Fixed global variable issues
- ✅ Removed unused functions
- ✅ Added proper type annotations
- ✅ Improved error handling

### Functionality
- ✅ Added autonomous agent approach using SQLDatabaseToolkit
- ✅ Enhanced UI with database schema exploration
- ✅ Real-time streaming of agent execution steps
- ✅ Better query result display
- ✅ Dual approach selection (Chain vs Agent)

### User Experience
- ✅ Better visual feedback and loading states
- ✅ Expandable database information section
- ✅ Clear distinction between approaches
- ✅ Improved button layouts and interactions

## Architecture

### Chain Approach
```
User Question → Generate Query → [Human Approval] → Execute Query → Generate Answer
```

### Agent Approach
```
User Question → Agent → [Multiple Tool Calls] → Final Answer
```

The agent can:
- List database tables
- Query table schemas
- Execute SQL queries
- Check query syntax
- Self-correct on errors

## Database Schema

The application connects to a SQL Server database with the following configuration:
- Server: `HAARLT0397\SQLEXPRESS`
- Database: `Visa_Data_05262025`
- Authentication: Windows Authentication (Trusted Connection)

## Troubleshooting

1. **API Key Issues**: Ensure your Google API key is properly set in environment variables or secrets
2. **Database Connection**: Verify SQL Server is running and accessible
3. **ODBC Driver**: Install ODBC Driver 17 for SQL Server if not present
4. **Dependencies**: Run `pip install -r requirements.txt` to ensure all packages are installed

## Next Steps

Consider implementing these additional features:
- Query validation and syntax checking
- Support for multiple database types
- Query history and favorites
- Export results to CSV/Excel
- Advanced filtering for high-cardinality columns
- Custom prompt templates for specific use cases
