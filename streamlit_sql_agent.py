import streamlit as st
import os
from typing_extensions import TypedDict
from langchain.chat_models import init_chat_model
from langchain_community.utilities import SQLDatabase
from langchain_community.tools.sql_database.tool import QuerySQLDatabaseTool
from langgraph.graph import START, StateGraph
from langgraph.checkpoint.memory import MemorySaver
from langchain_core.prompts import ChatPromptTemplate
import pyodbc

# Initialize Google API key from environment or Streamlit secrets
if "GOOGLE_API_KEY" not in os.environ:
    try:
        os.environ["GOOGLE_API_KEY"] = st.secrets["GOOGLE_API_KEY"]
    except:
        st.error("Please set GOOGLE_API_KEY in environment variables or Streamlit secrets")
        st.stop()

# Initialize globals
db = None
llm = None

# Database connection string
DB_CONN_STR = (
    r'DRIVER={ODBC Driver 17 for SQL Server};'
    r'SERVER=HAARLT0397\SQLEXPRESS;'
    r'DATABASE=Visa_Data_05262025;'
    r'Trusted_Connection=yes;'
)

class State(TypedDict):
    """Application state."""
    question: str
    query: str
    result: str
    answer: str

def init_db():
    """Initialize database connection"""
    return SQLDatabase.from_uri(f"mssql+pyodbc:///?odbc_connect={DB_CONN_STR}")

def init_llm():
    """Initialize the LLM"""
    return init_chat_model("gemini-2.0-flash", model_provider="google_genai")

def write_query(state: State):
    """Generate SQL query to fetch information."""
    global db, llm
    prompt = ChatPromptTemplate.from_messages([
        ("system", """
        Given an input question, create a syntactically correct SQL Server query to
        run to help find the answer. Unless the user specifies in their question a
        specific number of examples they wish to obtain, always limit your query to
        at most {top_k} results. You can order the results by a relevant column to
        return the most interesting examples in the database.

        Never query for all the columns from a specific table, only ask for the
        few relevant columns given the question.

        Pay attention to use only the column names you can see in the schema
        description. Be careful to not query for columns that do not exist.
        Also, pay attention to which column is in which table.

        Only use the following tables:
        {table_info}
        """),
        ("user", "Question: {input}")
    ])

    structured_llm = llm.with_structured_output({"query": str})
    result = structured_llm.invoke(
        prompt.invoke({
            "top_k": 10,
            "table_info": db.get_table_info(),
            "input": state["question"]
        })
    )
    return {"query": result["query"]}

def execute_query(state: State):
    """Execute SQL query."""
    global db
    execute_query_tool = QuerySQLDatabaseTool(db=db)
    return {"result": execute_query_tool.invoke(state["query"])}

def generate_answer(state: State):
    """Generate answer based on query results."""
    global llm
    prompt = (
        "Given the following user question, corresponding SQL query, "
        "and SQL result, answer the user question.\n\n"
        f'Question: {state["question"]}\n'
        f'SQL Query: {state["query"]}\n'
        f'SQL Result: {state["result"]}'
    )
    response = llm.invoke(prompt)
    return {"answer": response.content}

def create_graph():
    """Create the LangGraph workflow."""
    # Initialize graph
    graph_builder = StateGraph(State)
    
    # Add nodes in sequence
    graph_builder.add_node("write_query", write_query)
    graph_builder.add_node("execute_query", execute_query)
    graph_builder.add_node("generate_answer", generate_answer)
    
    # Add edges
    graph_builder.add_edge('write_query', 'execute_query')
    graph_builder.add_edge('execute_query', 'generate_answer')
    graph_builder.set_entry_point("write_query")
    
    # Add memory for persistence
    memory = MemorySaver()
    
    # Compile graph with human review before query execution
    return graph_builder.compile(
        checkpointer=memory,
        interrupt_before=["execute_query"]
    )



def main():
    global db, llm
    st.title("SQL Query Assistant")
    st.write("Ask questions about your SQL Server database!")

    # Initialize session state
    if 'db' not in st.session_state:
        st.session_state.db = init_db()
        st.session_state.llm = init_llm()
        st.session_state.graph = create_graph()
        st.session_state.thread_id = 0

    # Set global variables for use in node functions
    db = st.session_state.db
    llm = st.session_state.llm
    
    # Create input text area
    question = st.text_area("Enter your question:", height=100)
    
    if st.button("Get Answer"):
        if not question:
            st.warning("Please enter a question!")
            return
            
        # Increment thread ID for new questions
        st.session_state.thread_id += 1
        config = {"configurable": {"thread_id": str(st.session_state.thread_id)}}
        
        # Process starts here
        with st.spinner("Generating SQL query..."):
            # Start the graph execution
            steps_container = st.container()
            
            for step in st.session_state.graph.stream(
                {"question": question},
                config,
                stream_mode="updates"
            ):
                # Display each step
                with steps_container:
                    if "query" in step:
                        st.code(step["query"], language="sql")
                    elif "result" in step:
                        st.json(step["result"])
                    elif "answer" in step:
                        st.success(step["answer"])
                
                # If we hit the execute_query step, ask for approval
                if "query" in step:
                    st.session_state.current_query = step["query"]
                    st.write("### Review the SQL Query:")
                    st.code(step["query"], language="sql")
                    
                    col1, col2 = st.columns(2)
                    with col1:
                        if st.button("✅ Approve and Execute"):
                            with st.spinner("Executing query..."):
                                # Continue graph execution
                                for next_step in st.session_state.graph.stream(
                                    None, config, stream_mode="updates"
                                ):
                                    if "result" in next_step:
                                        st.json(next_step["result"])
                                    elif "answer" in next_step:
                                        st.success(next_step["answer"])
                    
                    with col2:
                        if st.button("❌ Cancel"):
                            st.error("Operation cancelled by user.")
                            break

if __name__ == "__main__":
    main()
