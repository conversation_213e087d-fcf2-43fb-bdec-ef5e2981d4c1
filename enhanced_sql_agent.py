import streamlit as st
import os
from typing_extensions import TypedDict, Annotated
from langchain.chat_models import init_chat_model
from langchain_community.utilities import SQLDatabase
from langchain_community.tools.sql_database.tool import QuerySQLDatabaseTool
from langchain_community.agent_toolkits import SQLDatabaseToolkit
from langgraph.graph import START, StateGraph
from langgraph.checkpoint.memory import MemorySaver
from langgraph.prebuilt import create_react_agent
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.messages import HumanMessage
import pyodbc

# Initialize Google API key from environment or Streamlit secrets
if "GOOGLE_API_KEY" not in os.environ:
    try:
        os.environ["GOOGLE_API_KEY"] = "AIzaSyCeRpmqPkfPKVPPV4CuQHyJY4JmtW5pyxk"
    except:
        st.error("Please set GOOGLE_API_KEY in environment variables or Streamlit secrets")
        st.stop()

# Database connection string
DB_CONN_STR = (
    r'DRIVER={ODBC Driver 17 for SQL Server};'
    r'SERVER=HAARLT0397\SQLEXPRESS;'
    r'DATABASE=Visa_Data_05262025;'
    r'Trusted_Connection=yes;'
)

class State(TypedDict):
    """Application state for the chain approach."""
    question: str
    query: str
    result: str
    answer: str

class QueryOutput(TypedDict):
    """Generated SQL query with type annotation."""
    query: Annotated[str, ..., "Syntactically valid SQL query."]

def init_db():
    """Initialize database connection"""
    return SQLDatabase.from_uri(f"mssql+pyodbc:///?odbc_connect={DB_CONN_STR}")

def init_llm():
    """Initialize the LLM"""
    return init_chat_model("gemini-2.0-flash", model_provider="google_genai")

# Chain approach functions
def write_query(state: State, db, llm):
    """Generate SQL query to fetch information."""
    system_message = """
    Given an input question, create a syntactically correct SQL Server query to
    run to help find the answer. Unless the user specifies in their question a
    specific number of examples they wish to obtain, always limit your query to
    at most {top_k} results. You can order the results by a relevant column to
    return the most interesting examples in the database.

    Never query for all the columns from a specific table, only ask for the
    few relevant columns given the question.

    Pay attention to use only the column names you can see in the schema
    description. Be careful to not query for columns that do not exist.
    Also, pay attention to which column is in which table.

    Only use the following tables:
    {table_info}
    """
    
    prompt = ChatPromptTemplate.from_messages([
        ("system", system_message),
        ("user", "Question: {input}")
    ])
    
    structured_llm = llm.with_structured_output(QueryOutput)
    result = structured_llm.invoke(
        prompt.invoke({
            "top_k": 10,
            "table_info": db.get_table_info(),
            "input": state["question"]
        })
    )
    return {"query": result["query"]}

def execute_query(state: State, db):
    """Execute SQL query."""
    execute_query_tool = QuerySQLDatabaseTool(db=db)
    return {"result": execute_query_tool.invoke(state["query"])}

def generate_answer(state: State, llm):
    """Generate answer based on query results."""
    prompt = (
        "Given the following user question, corresponding SQL query, "
        "and SQL result, answer the user question.\n\n"
        f'Question: {state["question"]}\n'
        f'SQL Query: {state["query"]}\n'
        f'SQL Result: {state["result"]}'
    )
    response = llm.invoke(prompt)
    return {"answer": response.content}

def create_chain_graph(db, llm):
    """Create the LangGraph workflow for chain approach."""
    def write_query_wrapper(state: State):
        return write_query(state, db, llm)
    
    def execute_query_wrapper(state: State):
        return execute_query(state, db)
    
    def generate_answer_wrapper(state: State):
        return generate_answer(state, llm)
    
    # Initialize graph
    graph_builder = StateGraph(State)
    
    # Add nodes in sequence
    graph_builder.add_node("write_query", write_query_wrapper)
    graph_builder.add_node("execute_query", execute_query_wrapper)
    graph_builder.add_node("generate_answer", generate_answer_wrapper)
    
    # Add edges
    graph_builder.add_edge('write_query', 'execute_query')
    graph_builder.add_edge('execute_query', 'generate_answer')
    graph_builder.set_entry_point("write_query")
    
    # Add memory for persistence
    memory = MemorySaver()
    
    # Compile graph with human review before query execution
    return graph_builder.compile(
        checkpointer=memory,
        interrupt_before=["execute_query"]
    )

def create_agent_executor(db, llm):
    """Create SQL agent using toolkit approach."""
    toolkit = SQLDatabaseToolkit(db=db, llm=llm)
    tools = toolkit.get_tools()
    
    system_message = """
    You are an agent designed to interact with a SQL database.
    Given an input question, create a syntactically correct SQL Server query to run,
    then look at the results of the query and return the answer. Unless the user
    specifies a specific number of examples they wish to obtain, always limit your
    query to at most 5 results.

    You can order the results by a relevant column to return the most interesting
    examples in the database. Never query for all the columns from a specific table,
    only ask for the relevant columns given the question.

    You MUST double check your query before executing it. If you get an error while
    executing a query, rewrite the query and try again.

    DO NOT make any DML statements (INSERT, UPDATE, DELETE, DROP etc.) to the
    database.

    To start you should ALWAYS look at the tables in the database to see what you
    can query. Do NOT skip this step.

    Then you should query the schema of the most relevant tables.
    """
    
    return create_react_agent(llm, tools, prompt=system_message)

def main():
    st.title("Enhanced SQL Query Assistant")
    st.write("Ask questions about your SQL Server database using either Chain or Agent approach!")
    
    # Initialize session state
    if 'db' not in st.session_state:
        with st.spinner("Initializing database connection..."):
            st.session_state.db = init_db()
            st.session_state.llm = init_llm()
            st.session_state.chain_graph = create_chain_graph(st.session_state.db, st.session_state.llm)
            st.session_state.agent_executor = create_agent_executor(st.session_state.db, st.session_state.llm)
            st.session_state.thread_id = 0
    
    # Sidebar for approach selection
    approach = st.sidebar.selectbox(
        "Select Approach:",
        ["Chain (with Human-in-the-loop)", "Agent (Autonomous)"],
        help="Chain approach requires approval before executing queries. Agent approach is fully autonomous."
    )
    
    # Display database info
    with st.expander("Database Information"):
        st.write("**Available Tables:**")
        tables = st.session_state.db.get_usable_table_names()
        st.write(", ".join(tables))
        
        if st.button("Show Table Schemas"):
            for table in tables[:3]:  # Show first 3 tables to avoid overwhelming
                st.write(f"**{table} Schema:**")
                schema_info = st.session_state.db.get_table_info([table])
                st.code(schema_info, language="sql")
    
    # Create input text area
    question = st.text_area("Enter your question:", height=100, 
                           placeholder="e.g., How many records are in the database? What are the top 5 customers by transaction amount?")
    
    if st.button("Get Answer", type="primary"):
        if not question:
            st.warning("Please enter a question!")
            return
        
        if approach == "Chain (with Human-in-the-loop)":
            handle_chain_approach(question)
        else:
            handle_agent_approach(question)

def handle_chain_approach(question):
    """Handle the chain approach with human-in-the-loop."""
    # Increment thread ID for new questions
    st.session_state.thread_id += 1
    config = {"configurable": {"thread_id": str(st.session_state.thread_id)}}
    
    with st.spinner("Generating SQL query..."):
        # Start the graph execution
        for step in st.session_state.chain_graph.stream(
            {"question": question},
            config,
            stream_mode="updates"
        ):
            if "write_query" in step:
                query = step["write_query"]["query"]
                st.write("### Generated SQL Query:")
                st.code(query, language="sql")
                
                # Ask for approval
                col1, col2 = st.columns(2)
                with col1:
                    if st.button("✅ Approve and Execute", key="approve"):
                        with st.spinner("Executing query..."):
                            # Continue graph execution
                            for next_step in st.session_state.chain_graph.stream(
                                None, config, stream_mode="updates"
                            ):
                                if "execute_query" in next_step:
                                    result = next_step["execute_query"]["result"]
                                    st.write("### Query Result:")
                                    st.code(result)
                                elif "generate_answer" in next_step:
                                    answer = next_step["generate_answer"]["answer"]
                                    st.write("### Final Answer:")
                                    st.success(answer)
                
                with col2:
                    if st.button("❌ Cancel", key="cancel"):
                        st.error("Operation cancelled by user.")
                        return

def handle_agent_approach(question):
    """Handle the autonomous agent approach."""
    with st.spinner("Agent is working on your question..."):
        # Create a container for streaming output
        output_container = st.container()
        
        with output_container:
            st.write("### Agent Execution Steps:")
            
            for step in st.session_state.agent_executor.stream(
                {"messages": [{"role": "user", "content": question}]},
                stream_mode="values",
            ):
                # Display the latest message
                latest_message = step["messages"][-1]
                
                if hasattr(latest_message, 'content') and latest_message.content:
                    if "Tool Calls:" in str(latest_message.content):
                        st.write("🔧 **Tool Call:**")
                        st.code(latest_message.content)
                    elif latest_message.content.startswith("The ") or latest_message.content.startswith("Based on"):
                        st.write("### Final Answer:")
                        st.success(latest_message.content)
                    else:
                        st.write("💭 **Agent Thinking:**")
                        st.write(latest_message.content)

if __name__ == "__main__":
    main()
